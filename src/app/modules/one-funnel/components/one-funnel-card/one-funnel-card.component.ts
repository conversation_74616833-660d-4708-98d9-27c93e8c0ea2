import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { TranslateService } from '@ngx-translate/core';
import { OneFunnelInsightModel } from '../../model/one-funnel.model';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { SetOneFunnelDetailAction } from '../../state/one-funnel.actions';
import { OneFunnelService } from '../../service/one-funnel.service';

@Component({
  selector: 'app-one-funnel-card',
  templateUrl: './one-funnel-card.component.html',
  styleUrls: ['./one-funnel-card.component.scss'],
})
export class OneFunnelCardComponent implements OnInit {
  @Input() insight: OneFunnelInsightModel;
  @Input() isDetailView: boolean = false;

  showSmuModal = false;
  smuForm: FormGroup;
  isSubmittingSmu = false;
  smuSubmissionSuccess = false;
  smuSubmissionError = '';

  constructor(
    private readonly customerModuleService: CustomerModuleService,
    private readonly store: Store,
    private readonly router: Router,
    private readonly formBuilder: FormBuilder,
    private readonly oneFunnelService: OneFunnelService,
    private readonly translateService: TranslateService
  ) {
    this.smuForm = this.formBuilder.group({
      smu: ['', [Validators.required, Validators.pattern(/^\d+$/)]]
    });
  }

  ngOnInit() {}

  getIconClass(iconName?: string): string {
    switch (iconName) {
      case 'edit':
        return 'icon-edit';
      case 'delete':
        return 'icon-delete';
      case 'add':
        return 'icon-add';
      case 'save':
        return 'icon-save';
      default:
        return 'icon-smu-reading';
    }
  }


  shouldShowSmuButton(): boolean {
    return this.insight?.flowKey === 'ONE_ST_004';
  }

  shouldShowGoToEquipmentButton(): boolean {
    return !!this.insight?.serialNumber;
  }

  onCardClick(): void {
    if (!this.isDetailView && this.insight) {
      this.store.dispatch(new SetOneFunnelDetailAction(this.insight));
      this.router.navigateByUrl('/one-funnel/detail');
    }
  }

  onGoToEquipment(): void {
    if (this.insight?.equipmentNumber) {
      this.customerModuleService.openEquipmentModule(
        this.insight.customerNumber,
        this.insight.equipmentNumber
      );
    }
  }

  onEnterSmu(): void {
    this.showSmuModal = true;
    this.smuSubmissionSuccess = false;
    this.smuSubmissionError = '';
    this.smuForm.reset();
  }

  onCloseSmuModal(): void {
    this.showSmuModal = false;
    this.smuSubmissionSuccess = false;
    this.smuSubmissionError = '';
    this.smuForm.reset();
  }

  onSubmitSmu(): void {
    if (this.smuForm.valid && !this.isSubmittingSmu) {
      this.isSubmittingSmu = true;
      this.smuSubmissionError = '';

      const smuValue = this.smuForm.get('smu')?.value;
      const serialNumber = this.insight?.serialNumber;

      if (!serialNumber) {
        this.smuSubmissionError = this.translateService.instant('_one_funnel_serial_not_available');
        this.isSubmittingSmu = false;
        return;
      }

      this.oneFunnelService.updateSmu(serialNumber, smuValue).subscribe({
        next: (response) => {
          this.isSubmittingSmu = false;
          this.smuSubmissionSuccess = true;
          // Close modal after 2 seconds
          setTimeout(() => {
            this.onCloseSmuModal();
          }, 2000);
        },
        error: (error) => {
          this.isSubmittingSmu = false;
          this.smuSubmissionError = error.message || this.translateService.instant('_one_funnel_smu_update_failed');
        }
      });
    }
  }
}
